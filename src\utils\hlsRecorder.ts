import Hls from 'hls.js';
import type { DownloadItem, DownloadProgress, LiveRecordingController } from '../types';
import { DownloadStatus } from '../types';
import { formatSize, formatSpeed } from './m3u8Parser';

/**
 * HLS直播流录制器
 * 使用hls.js库实现真正的直播流录制功能
 */
export class HlsRecorder {
  private hls: Hls | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];
  private startTime: number = 0;
  private recordedSize: number = 0;
  private isRecording: boolean = false;
  private abortController: AbortController;
  private updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void;
  private item: DownloadItem;
  private segmentCount: number = 0;
  private lastUpdateTime: number = 0;

  constructor(
    item: DownloadItem,
    updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void
  ) {
    this.item = item;
    this.updateProgress = updateProgress;
    this.abortController = new AbortController();
  }

  /**
   * 开始录制直播流
   */
  async startRecording(): Promise<ArrayBuffer[]> {
    if (!Hls.isSupported()) {
      throw new Error('当前浏览器不支持HLS.js');
    }

    this.startTime = Date.now();
    this.lastUpdateTime = this.startTime;
    this.isRecording = true;

    try {
      // 创建隐藏的video元素用于播放
      const video = document.createElement('video');
      video.muted = true;
      video.style.display = 'none';
      document.body.appendChild(video);

      // 初始化HLS
      this.hls = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: true, // 启用低延迟模式
        backBufferLength: 90, // 保持90秒的后缓冲
        maxBufferLength: 30,  // 最大缓冲30秒
        maxMaxBufferLength: 60, // 最大缓冲上限60秒
        liveSyncDurationCount: 3, // 从直播边缘延迟3个分片
        liveMaxLatencyDurationCount: 10, // 最大延迟10个分片
      });

      // 设置请求头
      if (this.item.requestHeaders) {
        const headers = Object.fromEntries(
          this.item.requestHeaders.map(h => [h.name, h.value])
        );

        this.hls.config.xhrSetup = (xhr: XMLHttpRequest) => {
          Object.entries(headers).forEach(([key, value]) => {
            xhr.setRequestHeader(key, value);
          });
        };
      }

      // 监听HLS事件
      this.setupHlsEventListeners(video);

      // 加载M3U8源
      this.hls.loadSource(this.item.url);
      this.hls.attachMedia(video);

      // 等待录制完成
      return new Promise((resolve, reject) => {
        const cleanup = () => {
          if (this.hls) {
            this.hls.destroy();
            this.hls = null;
          }
          if (video.parentNode) {
            video.parentNode.removeChild(video);
          }
          if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
            this.mediaRecorder.stop();
          }
        };

        // 监听中断信号
        this.abortController.signal.addEventListener('abort', async () => {
          console.log('HLS录制被用户停止');
          cleanup();
          const data = await this.getRecordedData();
          resolve(data);
        });

        // 设置录制完成回调
        this.onRecordingComplete = async () => {
          cleanup();
          const data = await this.getRecordedData();
          resolve(data);
        };

        // 设置错误回调
        this.onRecordingError = (error: Error) => {
          cleanup();
          reject(error);
        };
      });

    } catch (error) {
      this.isRecording = false;
      throw error;
    }
  }

  /**
   * 停止录制
   */
  stop(): void {
    console.log('停止HLS录制:', this.item.requestId);
    this.isRecording = false;
    this.abortController.abort();
  }

  /**
   * 获取录制控制器
   */
  getController(): LiveRecordingController {
    return {
      requestId: this.item.requestId,
      abortController: this.abortController,
      isRecording: this.isRecording,
      recordedSegments: [], // HLS录制不使用分片数组
      startTime: this.startTime,
      stop: async () => this.stop()
    };
  }

  /**
   * 设置HLS事件监听器
   */
  private setupHlsEventListeners(video: HTMLVideoElement): void {
    if (!this.hls) return;

    // 清单解析完成
    this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
      console.log('HLS清单解析完成');
      this.updateProgress(this.item.requestId, {
        status: DownloadStatus.LIVE_RECORDING,
        statusText: '开始录制直播流...',
        percentage: 10
      });

      // 开始播放
      video.play().catch(console.error);
    });

    // 媒体附加完成，等待视频准备就绪
    this.hls.on(Hls.Events.MEDIA_ATTACHED, () => {
      console.log('媒体附加完成，等待视频准备就绪');
      this.waitForVideoReady(video);
    });

    // 分片加载完成
    this.hls.on(Hls.Events.FRAG_LOADED, () => {
      this.segmentCount++;
      this.updateRecordingProgress();
    });

    // 直播流结束
    this.hls.on(Hls.Events.LEVEL_UPDATED, (_, data) => {
      if (data.details.live === false) {
        console.log('检测到直播流结束');
        setTimeout(() => {
          this.stop();
        }, 5000); // 等待5秒确保所有数据录制完成
      }
    });

    // 错误处理
    this.hls.on(Hls.Events.ERROR, (_, data) => {
      console.error('HLS错误:', data);

      if (data.fatal) {
        switch (data.type) {
          case Hls.ErrorTypes.NETWORK_ERROR:
            console.log('网络错误，尝试恢复...');
            this.hls?.startLoad();
            break;
          case Hls.ErrorTypes.MEDIA_ERROR:
            console.log('媒体错误，尝试恢复...');
            this.hls?.recoverMediaError();
            break;
          default:
            console.error('无法恢复的错误:', data);
            this.onRecordingError?.(new Error(`HLS错误: ${data.reason || data.type}`));
            break;
        }
      }
    });
  }

  /**
   * 等待视频准备就绪后开始录制
   */
  private async waitForVideoReady(video: HTMLVideoElement): Promise<void> {
    console.log('等待视频准备就绪...');

    // 等待视频元数据加载完成
    await new Promise<void>((resolve) => {
      if (video.readyState >= 1) { // HAVE_METADATA
        resolve();
      } else {
        const onLoadedMetadata = () => {
          video.removeEventListener('loadedmetadata', onLoadedMetadata);
          resolve();
        };
        video.addEventListener('loadedmetadata', onLoadedMetadata);
      }
    });

    // 等待视频可以播放
    await new Promise<void>((resolve) => {
      if (video.readyState >= 3) { // HAVE_FUTURE_DATA
        resolve();
      } else {
        const onCanPlay = () => {
          video.removeEventListener('canplay', onCanPlay);
          resolve();
        };
        video.addEventListener('canplay', onCanPlay);
      }
    });

    // 等待视频真正开始播放
    await new Promise<void>((resolve) => {
      if (!video.paused && video.currentTime > 0) {
        resolve();
      } else {
        const onPlaying = () => {
          video.removeEventListener('playing', onPlaying);
          // 额外等待一小段时间确保流稳定
          setTimeout(resolve, 500);
        };
        video.addEventListener('playing', onPlaying);
      }
    });

    console.log('视频准备就绪，开始录制');
    this.startMediaRecording(video);
  }

  /**
   * 开始媒体录制
   */
  private startMediaRecording(video: HTMLVideoElement): void {
    try {
      // 创建媒体流 - 扩展HTMLVideoElement接口以支持captureStream
      interface HTMLVideoElementWithCapture extends HTMLVideoElement {
        captureStream?: (frameRate?: number) => MediaStream;
        mozCaptureStream?: (frameRate?: number) => MediaStream;
      }

      const videoWithCapture = video as HTMLVideoElementWithCapture;
      const stream = videoWithCapture.captureStream ?
        videoWithCapture.captureStream() :
        videoWithCapture.mozCaptureStream?.();

      if (!stream) {
        throw new Error('浏览器不支持视频流捕获');
      }

      // 检查媒体流是否包含有效轨道
      const videoTracks = stream.getVideoTracks();
      const audioTracks = stream.getAudioTracks();

      console.log(`捕获的媒体流包含: ${videoTracks.length}个视频轨道, ${audioTracks.length}个音频轨道`);

      if (videoTracks.length === 0 && audioTracks.length === 0) {
        throw new Error('捕获的媒体流不包含任何轨道，请稍后重试');
      }

      // 获取支持的MIME类型
      const mimeType = this.getSupportedMimeType();
      console.log('使用MIME类型:', mimeType);

      // 创建MediaRecorder
      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: mimeType
      });

      // 监听数据可用事件
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          this.recordedChunks.push(event.data);
          this.recordedSize += event.data.size;
        }
      };

      // 监听录制停止事件
      this.mediaRecorder.onstop = () => {
        console.log('MediaRecorder录制停止');
        this.onRecordingComplete?.();
      };

      // 监听录制错误事件
      this.mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder录制错误:', event);
        this.onRecordingError?.(new Error('录制过程中发生错误'));
      };

      // 开始录制
      this.mediaRecorder.start(1000); // 每秒生成一个数据块
      console.log('MediaRecorder录制开始');

    } catch (error) {
      console.error('启动媒体录制失败:', error);
      this.onRecordingError?.(error as Error);
    }
  }

  /**
   * 获取浏览器支持的MIME类型
   */
  private getSupportedMimeType(): string {
    // 按优先级排序的MIME类型列表
    const mimeTypes = [
      'video/webm;codecs=vp9,opus',
      'video/webm;codecs=vp8,opus',
      'video/webm;codecs=h264,opus',
      'video/webm',
      'video/mp4;codecs=h264,aac',
      'video/mp4'
    ];

    // 查找第一个支持的类型
    for (const mimeType of mimeTypes) {
      if (MediaRecorder.isTypeSupported(mimeType)) {
        return mimeType;
      }
    }

    // 如果都不支持，返回默认类型
    console.warn('没有找到支持的MIME类型，使用默认类型');
    return 'video/webm';
  }

  /**
   * 更新录制进度
   */
  private updateRecordingProgress(): void {
    const currentTime = Date.now();

    // 限制更新频率
    if (currentTime - this.lastUpdateTime < 1000) return;
    this.lastUpdateTime = currentTime;

    const elapsedSeconds = Math.floor((currentTime - this.startTime) / 1000);
    const speed = elapsedSeconds > 0 ? this.recordedSize / elapsedSeconds : 0;

    this.updateProgress(this.item.requestId, {
      status: DownloadStatus.LIVE_RECORDING,
      statusText: `录制中 (${this.segmentCount}个分片, ${elapsedSeconds}秒) - ${formatSize(this.recordedSize)} - ${formatSpeed(speed)}`,
      percentage: Math.min(90, this.segmentCount * 2),
      downloadedSize: this.recordedSize,
      speed: speed
    });
  }

  /**
   * 获取录制的数据
   */
  private async getRecordedData(): Promise<ArrayBuffer[]> {
    if (this.recordedChunks.length === 0) {
      return [];
    }

    // 合并所有录制的数据块
    const blob = new Blob(this.recordedChunks, { type: 'video/webm' });

    // 转换为ArrayBuffer
    return new Promise<ArrayBuffer[]>((resolve) => {
      const reader = new FileReader();
      reader.onload = () => {
        resolve([reader.result as ArrayBuffer]);
      };
      reader.readAsArrayBuffer(blob);
    });
  }

  // 回调函数
  private onRecordingComplete?: () => void;
  private onRecordingError?: (error: Error) => void;
}
